/**
 * ByteRover - Quicksort Implementation
 * A fast and efficient sorting algorithm using divide-and-conquer approach
 */

/**
 * Quicksort algorithm implementation
 * @param {Array} arr - Array to be sorted
 * @param {number} low - Starting index (default: 0)
 * @param {number} high - Ending index (default: arr.length - 1)
 * @returns {Array} - Sorted array
 */
function quicksort(arr, low = 0, high = arr.length - 1) {
    if (low < high) {
        // Partition the array and get the pivot index
        const pivotIndex = partition(arr, low, high);
        
        // Recursively sort elements before and after partition
        quicksort(arr, low, pivotIndex - 1);
        quicksort(arr, pivotIndex + 1, high);
    }
    return arr;
}

/**
 * Partition function for quicksort
 * Places the pivot element at its correct position in sorted array
 * @param {Array} arr - Array to partition
 * @param {number} low - Starting index
 * @param {number} high - Ending index
 * @returns {number} - Index of the pivot element
 */
function partition(arr, low, high) {
    // Choose the rightmost element as pivot
    const pivot = arr[high];
    
    // Index of smaller element (indicates right position of pivot)
    let i = low - 1;
    
    for (let j = low; j < high; j++) {
        // If current element is smaller than or equal to pivot
        if (arr[j] <= pivot) {
            i++;
            swap(arr, i, j);
        }
    }
    
    // Place pivot at correct position
    swap(arr, i + 1, high);
    return i + 1;
}

/**
 * Swap two elements in an array
 * @param {Array} arr - Array containing elements to swap
 * @param {number} i - Index of first element
 * @param {number} j - Index of second element
 */
function swap(arr, i, j) {
    const temp = arr[i];
    arr[i] = arr[j];
    arr[j] = temp;
}

/**
 * Alternative quicksort implementation that returns a new array (immutable)
 * @param {Array} arr - Array to be sorted
 * @returns {Array} - New sorted array
 */
function quicksortImmutable(arr) {
    if (arr.length <= 1) {
        return arr;
    }
    
    const pivot = arr[Math.floor(arr.length / 2)];
    const left = [];
    const middle = [];
    const right = [];
    
    for (const element of arr) {
        if (element < pivot) {
            left.push(element);
        } else if (element > pivot) {
            right.push(element);
        } else {
            middle.push(element);
        }
    }
    
    return [...quicksortImmutable(left), ...middle, ...quicksortImmutable(right)];
}

// Example usage and testing
if (typeof module !== 'undefined' && module.exports) {
    // Node.js environment
    module.exports = { quicksort, quicksortImmutable, partition, swap };
} else {
    // Browser environment - add to global scope
    window.ByteRover = { quicksort, quicksortImmutable, partition, swap };
}

// Demo function to test the quicksort
function demo() {
    console.log('ByteRover Quicksort Demo');
    console.log('========================');
    
    const testArray1 = [64, 34, 25, 12, 22, 11, 90];
    const testArray2 = [3, 6, 8, 10, 1, 2, 1];
    const testArray3 = [5, 2, 8, 6, 1, 9, 4];
    
    console.log('Original array 1:', testArray1);
    console.log('Sorted array 1:', quicksort([...testArray1]));
    
    console.log('\nOriginal array 2:', testArray2);
    console.log('Sorted array 2 (immutable):', quicksortImmutable(testArray2));
    
    console.log('\nOriginal array 3:', testArray3);
    console.log('Sorted array 3:', quicksort([...testArray3]));
    
    // Performance test with larger array
    const largeArray = Array.from({ length: 1000 }, () => Math.floor(Math.random() * 1000));
    console.log('\nTesting with 1000 random numbers...');
    
    const startTime = performance.now();
    const sortedLarge = quicksort([...largeArray]);
    const endTime = performance.now();
    
    console.log(`Sorting completed in ${(endTime - startTime).toFixed(2)} milliseconds`);
    console.log('First 10 elements of sorted array:', sortedLarge.slice(0, 10));
    console.log('Last 10 elements of sorted array:', sortedLarge.slice(-10));
}

// Uncomment the line below to run the demo
// demo();
